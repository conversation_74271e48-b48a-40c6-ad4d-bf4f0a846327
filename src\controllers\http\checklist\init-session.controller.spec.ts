import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP init session controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./init-session.controller', {
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    setChecklistUsers: Sinon.stub().resolves()
                }
            }   
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                userIds: [uuid(), uuid()]
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request params are invalid', async () => {
        const controller = await esmock('./init-session.controller', {
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    setChecklistUsers: Sinon.stub().resolves()
                }
            }   
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: false
            },
            body: {
                userIds: [uuid(), uuid()]
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid data')
        expect(data).to.include('id')
    })


    it('returns an error if the request body is invalid', async () => {
        const controller = await esmock('./init-session.controller', {
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    setChecklistUsers: Sinon.stub().resolves()
                }
            }   
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                userIds: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid data')
        expect(data).to.include('userIds')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./init-session.controller', {
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    setChecklistUsers: Sinon.stub().rejects()
                }
            }   
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                userIds: [uuid(), uuid()]
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })
   

})