import logger from '@lcs/logger'
import type { Request, Response } from 'express'
import httpStatus from 'http-status'
import getUser from '../../../services/mssql/user/get.service.js'
import { RedisClient } from '../../../services/redis/client.service.js'
import { ChecklistUserResponse, checklistUserResponseSchema } from '../../../models/internal/checklist-user-response.js'
import updateEvaluationSession from '../../../services/mssql/session/update.service.js'
import { SessionModel } from '../../../models/session.model.js'
import createQuestionResponse from '../../../services/mssql/question-response/create.service.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import createStatement from '../../../services/amqp/lrs/create-statement.service.js'
import { getSystemConfig } from '../../../services/amqp/system/get-system-config.service.js'
import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'
import getEvaluation from '../../../services/mssql/evaluations/get.service.js'
import getAllQuestionsForEvaluation from '../../../services/mssql/questions/get-all-for-evaluation.service.js'
import { Statement } from '../../../models/internal/xapi/statement.js'
import { stripHtml } from 'string-strip-html'
import { QuestionTypes } from '@tess-f/sql-tables/dist/evaluations/question-type.js'
import { Activity } from '../../../models/internal/xapi/activity.js'
import { User } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { SystemConfig } from '@tess-f/system-config/dist/http/system-config.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { EventType } from '@tess-f/shared-config/dist/tess-config/logger.js'

const {  INTERNAL_SERVER_ERROR, NO_CONTENT, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP.submit-checklist-session', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {  
  let submitter: User | undefined
  let systemConfig: SystemConfig | undefined
  let userSessionData: Map<string, string> = new Map()
  let userResponses: ChecklistUserResponse[] | undefined

  try {
    const { id } = z.object({
      id: z.string().regex(/([\dA-Fa-f])/)
    }).parse(req.params)
    userResponses = z.array(checklistUserResponseSchema).parse(req.body)

    // first we need to get the info for the user submitting the checklist
    submitter = await getUser(req.session.userId)
    // we need to get the system config for the lrs
    systemConfig = await getSystemConfig()
    
    // next we need to get the checklist session data from redis
    userSessionData = await RedisClient.getUserSessionData(id)
    log('debug', 'Found user session data in Redis', { success: true, count: userSessionData.size, eventType: EventType.user_session_get })
    // don't make the user wait for us to loop through all this data and submit data to the LRS
    res.sendStatus(NO_CONTENT)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Invalid request data', { errorMessage: zodErrorToMessage(error), success: false, eventType: EventType.input_validation_errors })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
      log('error', 'Failed to get session data', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }

  if (submitter === undefined || systemConfig === undefined || userResponses === undefined) {
    log('error', 'Failed to process checklist response no submitter or system config', { success: false, req })
    return
  }

  try {
    // next we need to loop through each response and submit the session results and question responses to the database
    for (const userResponse of userResponses) {
      // we need to get the users session id form the session data map
      const sessionId = userSessionData.get(userResponse.UserId)
      log('debug', 'Found session id for user', { sessionId, userId: userResponse.UserId, success: true, eventType: EventType.user_session_get })

      const user = await getUser(userResponse.UserId)
      
      // Close out the session for this user
      const session = await updateEvaluationSession(new SessionModel({
        Id: sessionId,
        UserId: userResponse.UserId,
        Passed: userResponse.Passed,
        Notes: userResponse.Notes,
        End: new Date(),
        SubmittedBy: submitter.ID
      }))

      const evaluation = await getEvaluation(session.fields.EvalId ?? '')
      const questions = await getAllQuestionsForEvaluation(evaluation.Id ?? '', evaluation.Version ?? 1)

      for (const question of questions) {
        // gather the responses for this question
        const responses = userResponse.Responses.filter(r => r.QuestionId === question.Id || question.SubQuestions?.find(sq => sq.Id === r.QuestionId))

        // the duration should be the same for all responses
        const duration = responses.find(r => r.Duration)?.Duration

        // the notes should be the same for all responses
        const notes = responses.find(r => r.Notes)?.Notes

        // store the user response in the database
        for (const response of responses) {
          await createQuestionResponse(new QuestionResponseModel({
            SessionId: sessionId,
            QuestionId: response.QuestionId,
            QuestionVersion: response.QuestionVersion,
            OptionId: response.OptionId,
            OptionVersion: response.OptionVersion,
            Notes: response.Notes,
            Correct: true,
            Duration: response.Duration
          }))
        }

        // submit an xAPI statement about the question response(s)
        // we only want to submit statements for question responses that have options selected
        // durations will be added to the question response if it has one
        // notes will be added a a statement about the statement
        const optionResponses = responses.filter(r => r.OptionId !== undefined && r.OptionVersion !== undefined)
        for (const response of optionResponses) {
          const q = question.Id === response.QuestionId ? question : question.SubQuestions?.find(sq => sq.Id === response.QuestionId)
          if (q === undefined) {
            continue
          }

          const statement: Statement = {
            actor: {
              objectType: 'Agent',
              name: `${user.FirstName} ${user.LastName}`,
              account: {
                homePage: systemConfig.Domain,
                name: user.Username ?? ''
              }
            },
            verb: {
              id: CMI5Verbs.Completed
            },
            object: {
              objectType: 'Activity',
              id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/question/${q.Id}`,
              definition: {
                description: {
                  'en-US': `${stripHtml(q.Stem ?? '').result}`
                },
                type: 'http://adlnet.gov/expapi/activities/cmi.interaction'
              }
            },
            context: {
              extensions: {
                "https://w3id.org/xapi/cmi5/context/extensions/sessionid": session.fields.Id
              },
              instructor: {
                objectType: 'Agent',
                name: `${submitter.FirstName} ${submitter.LastName}`,
                account: {
                  homePage: systemConfig.Domain,
                  name: submitter.Username ?? ''
                }
              }
            },
            result: {
              response: response.OptionId
            },
            authority: {
              objectType: 'Group',
              member: [
                {
                  objectType: 'Agent',
                  name: `${submitter.FirstName} ${submitter.LastName}`,
                  account: {
                    homePage: systemConfig.Domain,
                    name: submitter.Username ?? ''
                  }
                },
                {
                  objectType: 'Agent',
                  name: 'Evaluation Engine',
                  mbox: 'mailto:<EMAIL>'
                }
              ]
            }
          }

          if (duration !== undefined) {
            statement.result!.duration = duration
          }

          if (q.QuestionTypeId === QuestionTypes.Checkbox) {
            (<Activity>statement.object).definition = {
              description: {
                'en-US': `${stripHtml(q.Stem ?? '').result}`
              },
              type: 'http://adlnet.gov/expapi/activities/cmi.interaction',
              interactionType: 'choice',
              choices: q.Options.map(o => {
                return {
                  id: o.Id ?? '',
                  description: {
                    'en-US': `${stripHtml(o.Text ?? '').result}`
                  }
                }
              })
            }

            statement.context!.contextActivities = {
              parent: [
                {
                  id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/${evaluation.Id}`
                }
              ]
            }
          } else if (q.QuestionTypeId === QuestionTypes.Likert) {
            (<Activity>statement.object).definition = {
              description: {
                'en-US': `${stripHtml(q.Stem ?? '').result}`
              },
              type: 'http://adlnet.gov/expapi/activities/cmi.interaction',
              interactionType: 'likert',
              scale: q.Options.map(o => {
                return {
                  id: o.Id ?? '',
                  description: {
                    'en-US': `${stripHtml(o.Text ?? '').result}`
                  }
                }
              })
            }
            statement.context!.contextActivities = {
              parent: [
                {
                  id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/question/${q.Id}`
                }
              ],
              grouping: [
                {
                  id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/${evaluation.Id}`
                }
              ]
            }
          }

          const statementId = await createStatement(statement)
          if (notes !== undefined && notes !== null) {
            await createStatement({
              actor: {
                objectType: 'Agent',
                name: `${submitter.FirstName} ${submitter.LastName}`,
                account: {
                  homePage: systemConfig.Domain,
                  name: submitter.Username ?? ''
                }
              },
              verb: { id: 'http://adlnet.gov/expapi/verbs/commented' },
              object: { objectType: 'StatementRef', id: statementId },
              result: { response: notes },
              context: {
                extensions: {
                  "https://w3id.org/xapi/cmi5/context/extensions/sessionid": session.fields.Id
                },
                instructor: {
                  objectType: 'Agent',
                  name: `${submitter.FirstName} ${submitter.LastName}`,
                  account: {
                    homePage: systemConfig.Domain,
                    name: submitter.Username ?? ''
                  }
                },
                contextActivities: {
                  parent: [
                    {
                      id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/${session.fields.EvalId}`
                    }
                  ]
                }
              },
              authority: {
                objectType: 'Group',
                member: [
                  {
                    objectType: 'Agent',
                    name: `${submitter.FirstName} ${submitter.LastName}`,
                    account: {
                      homePage: systemConfig.Domain,
                      name: submitter.Username ?? ''
                    }
                  },
                  {
                    objectType: 'Agent',
                    name: 'Evaluation Engine',
                    mbox: 'mailto:<EMAIL>'
                  }
                ]
              }
            })
          }
        }

        log('verbose', 'Submitted user responses for question', { questionId: question.Id, userId: user.ID, success: true, req })
      }

      // make an xAPI statement about the completion of the session
      const sessionStatementId = await createStatement({
        actor: {
          objectType: 'Agent',
          name: `${user.FirstName} ${user.LastName}`,
          account: {
            homePage: systemConfig.Domain,
            name: user.Username ?? ''
          }
        },
        verb: {
          id: userResponse.Passed ? CMI5Verbs.Passed : CMI5Verbs.Failed
        },
        object: {
          objectType: 'Activity',
          id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/${session.fields.EvalId}`,
        },
        context: {
          extensions: {
            "https://w3id.org/xapi/cmi5/context/extensions/sessionid": session.fields.Id
          },
          instructor: {
            objectType: 'Agent',
            name: `${submitter.FirstName} ${submitter.LastName}`,
            account: {
              homePage: systemConfig.Domain,
              name: submitter.Username ?? ''
            }
          }
        },
        authority: {
          objectType: 'Group',
          member: [
            {
              objectType: 'Agent',
              name: `${submitter.FirstName} ${submitter.LastName}`,
              account: {
                homePage: systemConfig.Domain,
                name: submitter.Username ?? ''
              }
            },
            {
              objectType: 'Agent',
              name: 'Evaluation Engine',
              mbox: 'mailto:<EMAIL>'
            }
          ]
        }
      })

      log('verbose', 'Submitted users completion data to the LRS', { userId: user.ID, success: true, req, eventType: EventType.statements_create })

      if (userResponse.Notes !== undefined && userResponse.Notes !== null && userResponse.Notes.length > 0) {
        // make an xAPI commented statement about the evaluators notes
        await createStatement({
          actor: {
            objectType: 'Agent',
            name: `${submitter.FirstName} ${submitter.LastName}`,
            account: {
              homePage: systemConfig.Domain,
              name: submitter.Username ?? ''
            }
          },
          verb: {
            id: 'http://adlnet.gov/expapi/verbs/commented'
          },
          object: {
            objectType: 'StatementRef',
            id: sessionStatementId
          },
          result: {
            response: userResponse.Notes
          },
          context: {
            extensions: {
              "https://w3id.org/xapi/cmi5/context/extensions/sessionid": session.fields.Id
            },
            instructor: {
              objectType: 'Agent',
              name: `${submitter.FirstName} ${submitter.LastName}`,
              account: {
                homePage: systemConfig.Domain,
                name: submitter.Username ?? ''
              }
            },
            contextActivities: {
              parent: [
                {
                  id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/${session.fields.EvalId}`
                }
              ]
            }
          },
          authority: {
            objectType: 'Group',
            member: [
              {
                objectType: 'Agent',
                name: `${submitter.FirstName} ${submitter.LastName}`,
                account: {
                  homePage: systemConfig.Domain,
                  name: submitter.Username ?? ''
                }
              },
              {
                objectType: 'Agent',
                name: 'Evaluation Engine',
                mbox: 'mailto:<EMAIL>'
              }
            ]
          }
        })
      }
    }
  } catch (error) {
    log('error', 'Failed to submit checklist', { success: false, error, req })
  }
}