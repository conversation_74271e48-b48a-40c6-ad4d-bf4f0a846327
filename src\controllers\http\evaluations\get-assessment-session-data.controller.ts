import logger from '@lcs/logger'
import type { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z, ZodError } from 'zod'
import getEvaluationSession from '../../../services/mssql/session/get.service.js'
import { EventType } from '@tess-f/shared-config/dist/tess-config/logger.js'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'


const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
const log = logger.create('Controller-HTTP.get-assessment-session-data', httpLogTransformer)

/**
 * Get assessment session data from the database.
 * This is called when a user is ready to start or view an assessment.
 * Retrieves the session that was created by init-assessment-session controller.
 *
 * GET /evaluation/session-data/:id
 * Params: { id: sessionId }
 */
export default async function getAssessmentSessionData(req: Request, res: Response): Promise<void> {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    // Get the assessment session from the database
    const session = await getEvaluationSession(id)

    log('info', 'Successfully retrieved assessment session from request', {
      sessionId: id,
      success: true,
      req,
      eventType: EventType.user_session_get
    })

    // Return the session data
    res.json(session.fields)

  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Invalid request data', {
        errorMessage: zodErrorToMessage(error),
        success: false,
        sessionId: req.params.id,
        req,
        eventType: EventType.input_validation_errors
      })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    }else if (getErrorMessage(error) === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'No session found', { error, success: false, req, sessionId: req.params.id, eventType: EventType.not_found_db })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get assessment session data', {
        sessionId: req.params.id,
        success: false,
        error,
        req,
        eventType: EventType.session_authority_logs
      })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
