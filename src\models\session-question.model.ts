import { Table } from '@lcs/mssql-utility'
import { EvalSessionQuestion, SessionQuestionFields, SessionQuestionsTableName } from '@tess-f/sql-tables/dist/evaluations/session-questions.js'

export class SessionQuestionModel extends Table<EvalSessionQuestion, EvalSessionQuestion> {
  fields: EvalSessionQuestion

  constructor (fields?: EvalSessionQuestion, record?: EvalSessionQuestion) {
    super(
      SessionQuestionsTableName,
      [
        SessionQuestionFields.SessionId,
        SessionQuestionFields.QuestionId,
        SessionQuestionFields.QuestionVersion
      ]
    )

    this.fields = fields ?? {}
    if (record) this.importFromDatabase(record)
  }

  importFromDatabase (record: EvalSessionQuestion): void {
    this.fields = {
      SessionId: record.SessionId,
      QuestionId: record.QuestionId,
      QuestionVersion: record.QuestionVersion,
      PresentationIndex: record.PresentationIndex,
      QuestionSetId: record.QuestionSetId,
      QuestionSetVersion: record.QuestionSetVersion
    }
  }

  exportJsonToDatabase (): EvalSessionQuestion {
    return {
      SessionId: this.fields.SessionId,
      QuestionId: this.fields.QuestionId,
      QuestionVersion: this.fields.QuestionVersion,
      PresentationIndex: this.fields.PresentationIndex,
      QuestionSetId: this.fields.QuestionSetId,
      QuestionSetVersion: this.fields.QuestionSetVersion
    }
  }
}
