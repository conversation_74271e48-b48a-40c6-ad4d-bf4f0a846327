import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import { v4 as uuid } from 'uuid'
import { CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'
import { EvaluationSectionWithQuestions } from '@tess-f/evaluations/dist/common/evaluation-section.js'
import { OptionVersion } from '@tess-f/sql-tables/dist/evaluations/option-version'

describe('Assessment Generator Service', () => {
    before(() => logger.init({ level: 'silly' }))
    afterEach(() => Sinon.restore())

    const mockEvaluation: CurrentEvaluationView = {
        Id: uuid(),
        Version: 1,
        EvaluationTypeId: 1,
        RandomizeQuestions: false
    } as CurrentEvaluationView



    it('processes fixed questions without QuestionSetId', async () => {
        const mockFixedQuestions = [
            {
                Id: uuid(),
                Version: 1,
                DisplayIndexWithinSection: 1,
                SectionDisplayIndex: 1,
                SectionId: 'section-1',
                SectionVersion: 1,
                SectionTitle: 'Section 1'
            }
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: mockFixedQuestions })  // Fixed questions query
            .onSecondCall().resolves({ recordset: [] })                 // Dynamic question sets query

        const assessmentGenerator = await esmock('./assessment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            },
            './save-assessment-service.js': {
                default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 1, optionsCount: 0 })
            }
        })

        const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

        expect(result).to.be.an('array')
        expect(result).to.have.length(1)
        expect(result[0]).to.have.property('SectionId')
        expect(result[0]).to.have.property('Questions')
        expect(result[0].Questions).to.have.length(1)
        expect(result[0].Questions[0].DisplayIndex).to.equal(1)
    })

    it('processes dynamic questions with QuestionSetId', async () => {
        const mockDynamicQuestionSets = [
            {
                Id: uuid(),
                Version: 1,
                SubsetSize: 2,
                DisplayIndexWithinSection: 2,
                SectionDisplayIndex: 1,
                SectionId: 'section-1',
                SectionVersion: 1,
                SectionTitle: 'Section 1'
            }
        ]

        const mockDynamicQuestions = [
            { Id: uuid(), Version: 1 },
            { Id: uuid(), Version: 1 }  // Only return 2 questions to match SubsetSize
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: [] })                      // Fixed questions query (empty)
            .onSecondCall().resolves({ recordset: mockDynamicQuestionSets }) // Dynamic question sets query
            .onThirdCall().resolves({ recordset: mockDynamicQuestions })     // Dynamic questions for set query

        const assessmentGenerator = await esmock('./assessment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            },
            './save-assessment-service.js': {
                default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 2, optionsCount: 0 })
            }
        })

        const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

        expect(result).to.be.an('array')
        expect(result).to.have.length(1) // One section
        expect(result[0].Questions).to.have.length(2) // SubsetSize limits to 2 questions
        expect(result[0].Questions[0].DisplayIndex).to.equal(2)
    })

    it('sorts questions by section DisplayIndex then question DisplayIndex', async () => {
        const mockFixedQuestions = [
            {
                Id: 'q1',
                Version: 1,
                DisplayIndexWithinSection: 2,
                SectionDisplayIndex: 2,
                SectionId: 'section-2',
                SectionVersion: 1,
                SectionTitle: 'Section 2'
            },
            {
                Id: 'q2',
                Version: 1,
                DisplayIndexWithinSection: 1,
                SectionDisplayIndex: 1,
                SectionId: 'section-1',
                SectionVersion: 1,
                SectionTitle: 'Section 1'
            },
            {
                Id: 'q3',
                Version: 1,
                DisplayIndexWithinSection: 1,
                SectionDisplayIndex: 2,
                SectionId: 'section-2',
                SectionVersion: 1,
                SectionTitle: 'Section 2'
            }
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: mockFixedQuestions })  // Fixed questions query
            .onSecondCall().resolves({ recordset: [] })                 // Dynamic question sets query (empty)

        const assessmentGenerator = await esmock('./assessment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            },
            './save-assessment-service.js': {
                default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 3, optionsCount: 0 })
            }
        })

        const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

        expect(result).to.be.an('array')
        expect(result).to.have.length(2) // Two sections
        // Section 1 should come first
        expect(result[0].DisplayIndex).to.equal(1)
        expect(result[0].Questions).to.have.length(1)
        expect(result[0].Questions[0].Id).to.equal('q2') // Section 1, DisplayIndex 1
        // Section 2 questions should be sorted by DisplayIndex
        expect(result[1].DisplayIndex).to.equal(2)
        expect(result[1].Questions).to.have.length(2)
        expect(result[1].Questions[0].Id).to.equal('q3') // Section 2, DisplayIndex 1
        expect(result[1].Questions[1].Id).to.equal('q1') // Section 2, DisplayIndex 2
    })

    it('randomizes questions within sections when RandomizeQuestions is true', async () => {
        const randomizedEvaluation = { ...mockEvaluation, RandomizeQuestions: true }

        const mockFixedQuestions = [
            { Id: 'q1', Version: 1, DisplayIndexWithinSection: 1, SectionDisplayIndex: 1, SectionId: 'section-1', SectionVersion: 1, SectionTitle: 'Section 1' },
            { Id: 'q2', Version: 1, DisplayIndexWithinSection: 2, SectionDisplayIndex: 1, SectionId: 'section-1', SectionVersion: 1, SectionTitle: 'Section 1' },
            { Id: 'q3', Version: 1, DisplayIndexWithinSection: 1, SectionDisplayIndex: 2, SectionId: 'section-2', SectionVersion: 1, SectionTitle: 'Section 2' }
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: mockFixedQuestions })
            .onSecondCall().resolves({ recordset: [] })

        const assessmentGenerator = await esmock('./assessment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            },
            './save-assessment-service.js': {
                default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 3, optionsCount: 0 })
            }
        })

        const result = await assessmentGenerator.default('test-user-id', randomizedEvaluation, 'test-session-id')
        
        expect(result).to.be.an('array')
        expect(result).to.have.length(2) // Two sections
        // Section order should be preserved (section 1 before section 2)
        expect(result[0].DisplayIndex).to.equal(1)
        expect(result[1].DisplayIndex).to.equal(2)
        // But questions within sections should be randomized
        expect(result[0].Questions).to.have.length(2)
        expect(result[1].Questions).to.have.length(1)
    })

    it('handles empty evaluation questions gracefully', async () => {
        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: [] })  // Fixed questions
            .onSecondCall().resolves({ recordset: [] }) // Dynamic question sets

        const assessmentGenerator = await esmock('./assessment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            },
            './save-assessment-service.js': {
                default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 0, optionsCount: 0 })
            }
        })

        const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

        expect(result).to.be.an('array')
        expect(result).to.have.length(0)
    })

    it('handles mixed fixed and dynamic questions', async () => {
        const dynamicSetId = uuid()
        const mockFixedQuestions = [
            {
                Id: 'fixed-q1',
                Version: 1,
                DisplayIndexWithinSection: 1,
                SectionDisplayIndex: 1,
                SectionId: 'section-1',
                SectionVersion: 1,
                SectionTitle: 'Section 1'
            }
        ]

        const mockDynamicSets = [
            {
                QuestionSetId: dynamicSetId,
                Version: 1,
                SubsetSize: 1,
                UseAll: false,
                DisplayIndexWithinSection: 2,
                SectionDisplayIndex: 1,
                SectionId: 'section-1',
                SectionVersion: 1,
                SectionTitle: 'Section 1'
            }
        ]

        const mockDynamicQuestions = [
            { Id: 'dynamic-q1', Version: 1 }
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: mockFixedQuestions })
            .onSecondCall().resolves({ recordset: mockDynamicSets })
            .onThirdCall().resolves({ recordset: mockDynamicQuestions })

        const assessmentGenerator = await esmock('./assessment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            },
            './save-assessment-service.js': {
                default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 2, optionsCount: 0 })
            }
        })

        const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')
        
        expect(result).to.be.an('array')
        expect(result).to.have.length(1) // One section
        expect(result[0].Questions).to.have.length(2) // Two questions in the section
        expect(result[0].Questions[0].Id).to.equal('fixed-q1')
        expect(result[0].Questions[1].Id).to.equal('dynamic-q1')
    })

    describe('Scramble Options Feature', () => {
        it('scrambles options when ScrambleOptions is true', async () => {
            const questionId = uuid()
            const mockFixedQuestions = [
                {
                    Id: questionId,
                    Version: 1,
                    DisplayIndexWithinSection: 1,
                    SectionDisplayIndex: 1,
                    SectionId: 'section-1',
                    SectionVersion: 1,
                    SectionTitle: 'Section 1',
                    ScrambleOptions: true
                }
            ]

            const mockOptions : OptionVersion[]= [
                { OptionId: 'opt-1', OrderId: 1, Locked: false, Text: 'Option 1' },
                { OptionId: 'opt-2', OrderId: 2, Locked: false, Text: 'Option 2' },
                { OptionId: 'opt-3', OrderId: 3, Locked: false, Text: 'Option 3' },
                { OptionId: 'opt-4', OrderId: 4, Locked: false, Text: 'Option 4' }
            ]

            const queryStub = Sinon.stub()
                .onFirstCall().resolves({ recordset: mockFixedQuestions })
                .onSecondCall().resolves({ recordset: [] })

            const getOptionsStub = Sinon.stub().resolves(mockOptions)

            const assessmentGenerator = await esmock('./assessment-generator-service.js', {
                '@lcs/mssql-utility': {
                    default: {
                        getPool: () => ({
                            request: () => ({
                                input: Sinon.stub().returnsThis(),
                                query: queryStub
                            })
                        })
                    }
                },
                '../mssql/question-options/get-for-question.service.js': {
                    default: getOptionsStub
                },
                '../mssql/questions/get-child-questions.service.js': {
                    default: Sinon.stub().resolves([])
                },
                './save-assessment-service.js': {
                    default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 1, optionsCount: 4 })
                }
            })

            const result: EvaluationSectionWithQuestions[] = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

            expect(result).to.be.an('array')
            expect(result).to.have.length(1)
            expect(result[0].Questions).to.have.length(1)
            expect(result[0].Questions[0].Options).to.have.length(4)

            // Verify all options are present (scrambled but complete)
            const optionIds = result[0].Questions[0].Options.map((opt: OptionVersion) => opt.OptionId)
            expect(optionIds).to.include.members(['opt-1', 'opt-2', 'opt-3', 'opt-4'])
        })

        it('keeps options in OrderId order when ScrambleOptions is false', async () => {
            const questionId = uuid()
            const mockFixedQuestions = [
                {
                    Id: questionId,
                    Version: 1,
                    DisplayIndexWithinSection: 1,
                    SectionDisplayIndex: 1,
                    SectionId: 'section-1',
                    SectionVersion: 1,
                    SectionTitle: 'Section 1',
                    ScrambleOptions: false
                }
            ]

            const mockOptions: OptionVersion[] = [
                { OptionId: 'opt-3', OrderId: 3, Locked: false, Text: 'Option 3' },
                { OptionId: 'opt-1', OrderId: 1, Locked: false, Text: 'Option 1' },
                { OptionId: 'opt-4', OrderId: 4, Locked: false, Text: 'Option 4' },
                { OptionId: 'opt-2', OrderId: 2, Locked: false, Text: 'Option 2' }
            ]

            const queryStub = Sinon.stub()
                .onFirstCall().resolves({ recordset: mockFixedQuestions })
                .onSecondCall().resolves({ recordset: [] })

            const getOptionsStub = Sinon.stub().resolves(mockOptions)

            const assessmentGenerator = await esmock('./assessment-generator-service.js', {
                '@lcs/mssql-utility': {
                    default: {
                        getPool: () => ({
                            request: () => ({
                                input: Sinon.stub().returnsThis(),
                                query: queryStub
                            })
                        })
                    }
                },
                '../mssql/question-options/get-for-question.service.js': {
                    default: getOptionsStub
                },
                '../mssql/questions/get-child-questions.service.js': {
                    default: Sinon.stub().resolves([])
                },
                './save-assessment-service.js': {
                    default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 1, optionsCount: 4 })
                }
            })

            const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

            expect(result).to.be.an('array')
            expect(result).to.have.length(1)
            expect(result[0].Questions).to.have.length(1)
            expect(result[0].Questions[0].Options).to.have.length(4)

            // Verify options are sorted by OrderId
            const optionIds = result[0].Questions[0].Options.map((opt: OptionVersion) => opt.OptionId)
            expect(optionIds).to.deep.equal(['opt-1', 'opt-2', 'opt-3', 'opt-4'])
        })

        it('respects locked options when scrambling', async () => {
            const questionId = uuid()
            const mockFixedQuestions = [
                {
                    Id: questionId,
                    Version: 1,
                    DisplayIndexWithinSection: 1,
                    SectionDisplayIndex: 1,
                    SectionId: 'section-1',
                    SectionVersion: 1,
                    SectionTitle: 'Section 1',
                    ScrambleOptions: true
                }
            ]

            const mockOptions = [
                { Id: 'opt-1', OrderId: 1, Locked: true, Text: 'None of the above' },
                { Id: 'opt-2', OrderId: 2, Locked: false, Text: 'Option 2' },
                { Id: 'opt-3', OrderId: 3, Locked: false, Text: 'Option 3' },
                { Id: 'opt-4', OrderId: 4, Locked: true, Text: 'All of the above' }
            ]

            const queryStub = Sinon.stub()
                .onFirstCall().resolves({ recordset: mockFixedQuestions })
                .onSecondCall().resolves({ recordset: [] })

            const getOptionsStub = Sinon.stub().resolves(mockOptions)

            const assessmentGenerator = await esmock('./assessment-generator-service.js', {
                '@lcs/mssql-utility': {
                    default: {
                        getPool: () => ({
                            request: () => ({
                                input: Sinon.stub().returnsThis(),
                                query: queryStub
                            })
                        })
                    }
                },
                '../mssql/question-options/get-for-question.service.js': {
                    default: getOptionsStub
                },
                '../mssql/questions/get-child-questions.service.js': {
                    default: Sinon.stub().resolves([])
                },
                './save-assessment-service.js': {
                    default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 1, optionsCount: 4 })
                }
            })

            const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

            expect(result).to.be.an('array')
            expect(result).to.have.length(1)
            expect(result[0].Questions).to.have.length(1)
            expect(result[0].Questions[0].Options).to.have.length(4)

            // Verify locked options stay in their original positions
            const options = result[0].Questions[0].Options
            expect(options[0].Id).to.equal('opt-1') // First position locked
            expect(options[3].Id).to.equal('opt-4') // Last position locked

            // Verify middle options are present (may be scrambled)
            const middleOptionIds = [options[1].Id, options[2].Id]
            expect(middleOptionIds).to.include.members(['opt-2', 'opt-3'])
        })

        it('scrambles sub-question options when ScrambleOptions is true', async () => {
            const questionId = uuid()
            const subQuestionId = uuid()
            const mockFixedQuestions = [
                {
                    Id: questionId,
                    Version: 1,
                    DisplayIndexWithinSection: 1,
                    SectionDisplayIndex: 1,
                    SectionId: 'section-1',
                    SectionVersion: 1,
                    SectionTitle: 'Section 1',
                    ScrambleOptions: false
                }
            ]

            const mockSubQuestions = [
                {
                    Id: subQuestionId,
                    Version: 1,
                    ScrambleOptions: true
                }
            ]

            const mockMainOptions: OptionVersion[] = [
                { OptionId: 'main-opt-1', OrderId: 1, Locked: false, Text: 'Main Option 1' }
            ]

            const mockSubOptions: OptionVersion[] = [
                { OptionId: 'sub-opt-1', OrderId: 1, Locked: false, Text: 'Sub Option 1' },
                { OptionId: 'sub-opt-2', OrderId: 2, Locked: false, Text: 'Sub Option 2' },
                { OptionId: 'sub-opt-3', OrderId: 3, Locked: false, Text: 'Sub Option 3' }
            ]

            const queryStub = Sinon.stub()
                .onFirstCall().resolves({ recordset: mockFixedQuestions })
                .onSecondCall().resolves({ recordset: [] })

            const getOptionsStub = Sinon.stub()
            getOptionsStub.withArgs(questionId, 1).resolves(mockMainOptions)
            getOptionsStub.withArgs(subQuestionId, 1).resolves(mockSubOptions)

            const getChildQuestionsStub = Sinon.stub().resolves(mockSubQuestions)

            const assessmentGenerator = await esmock('./assessment-generator-service.js', {
                '@lcs/mssql-utility': {
                    default: {
                        getPool: () => ({
                            request: () => ({
                                input: Sinon.stub().returnsThis(),
                                query: queryStub
                            })
                        })
                    }
                },
                '../mssql/question-options/get-for-question.service.js': {
                    default: getOptionsStub
                },
                '../mssql/questions/get-child-questions.service.js': {
                    default: getChildQuestionsStub
                },
                './save-assessment-service.js': {
                    default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 2, optionsCount: 4 })
                }
            })

            const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

            expect(result).to.be.an('array')
            expect(result).to.have.length(1)
            expect(result[0].Questions).to.have.length(1)
            expect(result[0].Questions[0].SubQuestions).to.have.length(1)
            expect(result[0].Questions[0].SubQuestions[0].Options).to.have.length(3)

            // Verify all sub-question options are present
            const subOptionIds = result[0].Questions[0].SubQuestions[0].Options.map((opt: OptionVersion) => opt.OptionId)
            expect(subOptionIds).to.include.members(['sub-opt-1', 'sub-opt-2', 'sub-opt-3'])
        })

        it('handles questions with no options', async () => {
            const questionId = uuid()
            const mockFixedQuestions = [
                {
                    Id: questionId,
                    Version: 1,
                    DisplayIndexWithinSection: 1,
                    SectionDisplayIndex: 1,
                    SectionId: 'section-1',
                    SectionVersion: 1,
                    SectionTitle: 'Section 1',
                    ScrambleOptions: true
                }
            ]

            const queryStub = Sinon.stub()
                .onFirstCall().resolves({ recordset: mockFixedQuestions })
                .onSecondCall().resolves({ recordset: [] })

            const getOptionsStub = Sinon.stub().resolves([])

            const assessmentGenerator = await esmock('./assessment-generator-service.js', {
                '@lcs/mssql-utility': {
                    default: {
                        getPool: () => ({
                            request: () => ({
                                input: Sinon.stub().returnsThis(),
                                query: queryStub
                            })
                        })
                    }
                },
                '../mssql/question-options/get-for-question.service.js': {
                    default: getOptionsStub
                },
                '../mssql/questions/get-child-questions.service.js': {
                    default: Sinon.stub().resolves([])
                },
                './save-assessment-service.js': {
                    default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 1, optionsCount: 0 })
                }
            })

            const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

            expect(result).to.be.an('array')
            expect(result).to.have.length(1)
            expect(result[0].Questions).to.have.length(1)
            expect(result[0].Questions[0].Options).to.have.length(0)
        })

        it('handles questions with all locked options', async () => {
            const questionId = uuid()
            const mockFixedQuestions = [
                {
                    Id: questionId,
                    Version: 1,
                    DisplayIndexWithinSection: 1,
                    SectionDisplayIndex: 1,
                    SectionId: 'section-1',
                    SectionVersion: 1,
                    SectionTitle: 'Section 1',
                    ScrambleOptions: true
                }
            ]

            const mockOptions: OptionVersion[] = [
                { OptionId: 'opt-1', OrderId: 1, Locked: true, Text: 'Option 1' },
                { OptionId: 'opt-2', OrderId: 2, Locked: true, Text: 'Option 2' },
                { OptionId: 'opt-3', OrderId: 3, Locked: true, Text: 'Option 3' }
            ]

            const queryStub = Sinon.stub()
                .onFirstCall().resolves({ recordset: mockFixedQuestions })
                .onSecondCall().resolves({ recordset: [] })

            const getOptionsStub = Sinon.stub().resolves(mockOptions)

            const assessmentGenerator = await esmock('./assessment-generator-service.js', {
                '@lcs/mssql-utility': {
                    default: {
                        getPool: () => ({
                            request: () => ({
                                input: Sinon.stub().returnsThis(),
                                query: queryStub
                            })
                        })
                    }
                },
                '../mssql/question-options/get-for-question.service.js': {
                    default: getOptionsStub
                },
                '../mssql/questions/get-child-questions.service.js': {
                    default: Sinon.stub().resolves([])
                },
                './save-assessment-service.js': {
                    default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 1, optionsCount: 3 })
                }
            })

            const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

            expect(result).to.be.an('array')
            expect(result).to.have.length(1)
            expect(result[0].Questions).to.have.length(1)
            expect(result[0].Questions[0].Options).to.have.length(3)

            // Verify all locked options stay in their original order
            const optionIds = result[0].Questions[0].Options.map((opt: OptionVersion) => opt.OptionId)
            expect(optionIds).to.deep.equal(['opt-1', 'opt-2', 'opt-3'])
        })

        it('handles match questions with source/target separation (fallback logic)', async () => {
            const questionId = uuid()
            const mockFixedQuestions = [
                {
                    Id: questionId,
                    Version: 1,
                    DisplayIndexWithinSection: 1,
                    SectionDisplayIndex: 1,
                    SectionId: 'section-1',
                    SectionVersion: 1,
                    SectionTitle: 'Section 1',
                    ScrambleOptions: true,
                    QuestionTypeId: 999 // Using a placeholder ID that won't match any known types
                }
            ]

            // Mock options for a match question - first half are source, second half are target
            const mockOptions: OptionVersion[] = [
                { OptionId: 'source-1', OrderId: 1, Locked: false, Text: 'Source Option 1' },
                { OptionId: 'source-2', OrderId: 2, Locked: false, Text: 'Source Option 2' },
                { OptionId: 'target-1', OrderId: 3, Locked: false, Text: 'Target Option 1' },
                { OptionId: 'target-2', OrderId: 4, Locked: false, Text: 'Target Option 2' }
            ]

            const queryStub = Sinon.stub()
                .onFirstCall().resolves({ recordset: mockFixedQuestions })
                .onSecondCall().resolves({ recordset: [] })

            const getOptionsStub = Sinon.stub().resolves(mockOptions)

            const assessmentGenerator = await esmock('./assessment-generator-service.js', {
                '@lcs/mssql-utility': {
                    default: {
                        getPool: () => ({
                            request: () => ({
                                input: Sinon.stub().returnsThis(),
                                query: queryStub
                            })
                        })
                    }
                },
                '../mssql/question-options/get-for-question.service.js': {
                    default: getOptionsStub
                },
                '../mssql/questions/get-child-questions.service.js': {
                    default: Sinon.stub().resolves([])
                },
                './save-assessment-service.js': {
                    default: Sinon.stub().resolves({ sessionId: 'test-session-id', questionsCount: 1, optionsCount: 4 })
                }
            })

            const result = await assessmentGenerator.default('test-user-id', mockEvaluation, 'test-session-id')

            expect(result).to.be.an('array')
            expect(result).to.have.length(1)
            expect(result[0].Questions).to.have.length(1)
            expect(result[0].Questions[0].Options).to.have.length(4)

            // Since isMatchQuestion returns false for our test ID, this will use regular scrambling
            // But the infrastructure is in place for when match questions are properly identified
            const optionIds = result[0].Questions[0].Options.map((opt: OptionVersion) => opt.OptionId)
            expect(optionIds).to.include.members(['source-1', 'source-2', 'target-1', 'target-2'])
        })
    })
})