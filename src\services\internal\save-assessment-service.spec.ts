import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import { v4 as uuid } from 'uuid'
import type { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import type { SaveAssessmentRequest } from '../../models/internal/save-assessment.js'

import mssql from '@lcs/mssql-utility'

describe('Save Assessment Service', () => {
  let createdSessionIds: string[] = []

  before(async () => {
    logger.init({ level: 'silly' })
  })

  afterEach(async () => {
    Sinon.restore()

    // Clean up any created test data from database tests
    for (const sessionId of createdSessionIds) {
      try {
        // Clean up in reverse order: options -> questions -> session
        await mssql.getPool().request()
          .input('sessionId', sessionId)
          .query('DELETE FROM EVAL_SessionOptions WHERE SessionId = @sessionId')

        await mssql.getPool().request()
          .input('sessionId', sessionId)
          .query('DELETE FROM EVAL_SessionQuestions WHERE SessionId = @sessionId')

        await mssql.getPool().request()
          .input('sessionId', sessionId)
          .query('DELETE FROM EVAL_Sessions WHERE Id = @sessionId')
      } catch (error) {
        console.warn(`Failed to clean up session ${sessionId}:`, error)
      }
    }
    createdSessionIds = []
  })

  const mockSessionId = uuid()
  const mockEvalId = uuid()
  const mockUserId = uuid()
  const mockQuestionId1 = uuid()
  const mockQuestionId2 = uuid()
  const mockOptionId1 = uuid()
  const mockOptionId2 = uuid()
  const mockOptionId3 = uuid()

  const mockQuestions: QuestionWithOptions[] = [
    {
      Id: mockQuestionId1,
      Version: 1,
      Options: [
        { Id: mockOptionId1, Version: 1 },
        { Id: mockOptionId2, Version: 1 }
      ]
    } as QuestionWithOptions,
    {
      Id: mockQuestionId2,
      Version: 2,
      Options: [
        { Id: mockOptionId3, Version: 1 }
      ]
    } as QuestionWithOptions
  ]

  const validRequest: SaveAssessmentRequest = {
    evalId: mockEvalId,
    evalVersion: 1,
    userId: mockUserId,
    questions: mockQuestions,
    notes: 'Test assessment',
    sessionId: mockSessionId
  }

  it('should successfully save an assessment with questions and options', async () => {
    const createQuestionStub = Sinon.stub().resolves({})
    const createOptionStub = Sinon.stub().resolves({})

    const saveAssessmentService = await esmock('./save-assessment-service.js', {
      '../mssql/session-questions/create.service.js': {
        default: createQuestionStub
      },
      '../mssql/session-options/create.service.js': {
        default: createOptionStub
      }
    })

    const result = await saveAssessmentService.default(validRequest)
    console.log(result)
    // Verify the result
    expect(result.sessionId).to.equal(mockSessionId)
    expect(result.questionsCount).to.equal(2)
    expect(result.optionsCount).to.equal(3)

    // Verify questions were created with correct presentation indices
    expect(createQuestionStub.callCount).to.equal(2)

    const question1Call = createQuestionStub.getCall(0)
    expect(question1Call.args[0].fields.SessionId).to.equal(mockSessionId)
    expect(question1Call.args[0].fields.QuestionId).to.equal(mockQuestionId1)
    expect(question1Call.args[0].fields.PresentationIndex).to.equal(0)

    const question2Call = createQuestionStub.getCall(1)
    expect(question2Call.args[0].fields.SessionId).to.equal(mockSessionId)
    expect(question2Call.args[0].fields.QuestionId).to.equal(mockQuestionId2)
    expect(question2Call.args[0].fields.PresentationIndex).to.equal(1)

    // Verify options were created with correct presentation indices
    expect(createOptionStub.callCount).to.equal(3)

    const option1Call = createOptionStub.getCall(0)
    expect(option1Call.args[0].fields.SessionId).to.equal(mockSessionId)
    expect(option1Call.args[0].fields.OptionId).to.equal(mockOptionId1)
    expect(option1Call.args[0].fields.PresentationIndex).to.equal(0)

    const option2Call = createOptionStub.getCall(1)
    expect(option2Call.args[0].fields.OptionId).to.equal(mockOptionId2)
    expect(option2Call.args[0].fields.PresentationIndex).to.equal(1)

    const option3Call = createOptionStub.getCall(2)
    expect(option3Call.args[0].fields.OptionId).to.equal(mockOptionId3)
    expect(option3Call.args[0].fields.PresentationIndex).to.equal(0) // First option of second question
  })

  it('should handle questions without options', async () => {
    const questionsWithoutOptions: QuestionWithOptions[] = [
      {
        Id: mockQuestionId1,
        Version: 1,
        Options: []
      } as QuestionWithOptions
    ]

    const requestWithoutOptions: SaveAssessmentRequest = {
      ...validRequest,
      questions: questionsWithoutOptions
    }

    const createQuestionStub = Sinon.stub().resolves({})
    const createOptionStub = Sinon.stub().resolves({})

    const saveAssessmentService = await esmock('./save-assessment-service.js', {
      '../mssql/session-questions/create.service.js': {
        default: createQuestionStub
      },
      '../mssql/session-options/create.service.js': {
        default: createOptionStub
      }
    })

    const result = await saveAssessmentService.default(requestWithoutOptions)

    expect(result.questionsCount).to.equal(1)
    expect(result.optionsCount).to.equal(0)
    expect(createQuestionStub.callCount).to.equal(1)
    expect(createOptionStub.callCount).to.equal(0)
  })

  it('should throw error for invalid request data', async () => {
    const invalidRequest = {
      evalId: 'invalid-guid',
      evalVersion: -1,
      userId: mockUserId,
      questions: []
    }

    const saveAssessmentService = await esmock('./save-assessment-service.js', {})

    try {
      await saveAssessmentService.default(invalidRequest)
      expect.fail('Should have thrown validation error')
    } catch (error) {
      expect(error).to.be.instanceOf(Error)
      expect((error as Error).message).to.include('Invalid request data')
    }
  })

  it('should save dynamic questions with QuestionSetId and QuestionSetVersion', async () => {
    const mockQuestionSetId = uuid()
    const mockQuestionSetVersion = 1
    const dynamicQuestions: QuestionWithOptions[] = [
      {
        Id: mockQuestionId1,
        Version: 1,
        QuestionSetId: mockQuestionSetId,
        QuestionSetVersion: mockQuestionSetVersion,
        Options: [
          { Id: mockOptionId1, Version: 1 }
        ]
      } as QuestionWithOptions
    ]

    const dynamicRequest: SaveAssessmentRequest = {
      evalId: mockEvalId,
      evalVersion: 1,
      userId: mockUserId,
      questions: dynamicQuestions,
      sessionId: mockSessionId
    }

    const createQuestionStub = Sinon.stub().resolves({})
    const createOptionStub = Sinon.stub().resolves({})

    const saveAssessmentService = await esmock('./save-assessment-service.js', {
      '../mssql/session-questions/create.service.js': {
        default: createQuestionStub
      },
      '../mssql/session-options/create.service.js': {
        default: createOptionStub
      }
    })

    const result = await saveAssessmentService.default(dynamicRequest)

    // Verify the result
    expect(result.sessionId).to.equal(mockSessionId)
    expect(result.questionsCount).to.equal(1)
    expect(result.optionsCount).to.equal(1)

    // Verify dynamic question was created with QuestionSetId and QuestionSetVersion
    expect(createQuestionStub.callCount).to.equal(1)
    const questionCall = createQuestionStub.getCall(0)
    expect(questionCall.args[0].fields.SessionId).to.equal(mockSessionId)
    expect(questionCall.args[0].fields.QuestionId).to.equal(mockQuestionId1)
    expect(questionCall.args[0].fields.QuestionSetId).to.equal(mockQuestionSetId)
    expect(questionCall.args[0].fields.QuestionSetVersion).to.equal(mockQuestionSetVersion)
    expect(questionCall.args[0].fields.PresentationIndex).to.equal(0)
  })



  // Integration test that actually uses the database
  // Note: This requires valid test data in your database
  it.skip('should actually save to database (integration test)', async () => {
    // Initialize database connection for integration test
    const settings = await import('../../config/settings.js')
    await mssql.init(settings.default.mssql.connectionConfig, false, 50)

    // Replace these with valid IDs from your test database
    const testEvalId = '9CFB423E-F36B-1410-8025-004AC39F4610' // Replace with valid eval ID
    const testUserId = mockUserId // Replace with valid user ID if needed
    const testQuestionId1 = mockQuestionId1 // Replace with valid question ID
    const testQuestionId2 = mockQuestionId2 // Replace with valid question ID
    const testOptionId1 = mockOptionId1 // Replace with valid option ID
    const testOptionId2 = mockOptionId2 // Replace with valid option ID
    const testOptionId3 = mockOptionId3 // Replace with valid option ID

    const realRequest: SaveAssessmentRequest = {
      evalId: testEvalId,
      evalVersion: 1,
      userId: testUserId,
      questions: [
        {
          Id: testQuestionId1,
          Version: 1,
          Options: [
            { Id: testOptionId1, Version: 1 },
            { Id: testOptionId2, Version: 1 }
          ]
        } as QuestionWithOptions,
        {
          Id: testQuestionId2,
          Version: 1,
          Options: [
            { Id: testOptionId3, Version: 1 }
          ]
        } as QuestionWithOptions
      ],
      notes: 'Real database integration test',
      sessionId: mockSessionId
    }

    // Import the actual service without mocking
    const { default: realSaveAssessmentService } = await import('./save-assessment-service.js')

    const result = await realSaveAssessmentService(realRequest)

    // Track for cleanup
    createdSessionIds.push(result.sessionId)

    // Verify the result
    expect(result.sessionId).to.be.a('string')
    expect(result.questionsCount).to.equal(2)
    expect(result.optionsCount).to.equal(3)

    // Verify data was actually saved to database
    const sessionCheck = await mssql.getPool().request()
      .input('sessionId', result.sessionId)
      .query('SELECT * FROM EVAL_Sessions WHERE Id = @sessionId')

    expect(sessionCheck.recordset).to.have.length(1)
    expect(sessionCheck.recordset[0].EvalId).to.equal(testEvalId)
    expect(sessionCheck.recordset[0].Notes).to.equal('Real database integration test')

    const questionsCheck = await mssql.getPool().request()
      .input('sessionId', result.sessionId)
      .query('SELECT COUNT(*) as count FROM EVAL_SessionQuestions WHERE SessionId = @sessionId')

    expect(questionsCheck.recordset[0].count).to.equal(2)

    const optionsCheck = await mssql.getPool().request()
      .input('sessionId', result.sessionId)
      .query('SELECT COUNT(*) as count FROM EVAL_SessionOptions WHERE SessionId = @sessionId')

    expect(optionsCheck.recordset[0].count).to.equal(3)
  })
})
