
import logger from '@lcs/logger'
import { SessionQuestionModel } from '../../models/session-question.model.js'
import { SessionOptionModel } from '../../models/session-option.model.js'
import createSessionQuestion from '../mssql/session-questions/create.service.js'
import createSessionOption from '../mssql/session-options/create.service.js'
import { prettifyError, z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { getErrorMessage } from '@tess-f/backend-utils'
import { SaveAssessmentRequest, SaveAssessmentResponse } from '../../models/internal/save-assessment.js'
import { EventType } from '@tess-f/shared-config'
import { EvalSessionQuestion } from '@tess-f/sql-tables/dist/evaluations/session-questions.js'

const log = logger.create('save-assessment-service')

// Validation schema for the save assessment request
const saveAssessmentRequestSchema = z.object({
  evalId: zodGUID,
  evalVersion: z.number().int().positive(),
  userId: zodGUID,
  questions: z.array(z.object({
    Id: zodGUID,
    Version: z.number().int().min(1),
    Options: z.array(z.object({
      Id: zodGUID,
      Version: z.number().int().min(1)
    })).default([]),
    SubQuestions: z.array(z.lazy(() => z.object({
      Id: zodGUID,
      Version: z.number().int().min(1),
      Options: z.array(z.object({
        Id: zodGUID,
        Version: z.number().int().min(1)
      })).default([])
    }))).optional()
  }).loose()).min(1), // passthrough allows additional QuestionVersion properties
  sessionId: zodGUID,
  notes: z.string().optional()
})

/**
 * Saves options for a question to the database
 * @param sessionId The session ID
 * @param questionId The question ID
 * @param questionVersion The question version
 * @param options Array of options to save
 * @returns Number of options saved
 */
async function saveQuestionOptions(
  sessionId: string,
  questionId: string,
  questionVersion: number,
  options: Array<{ Id: string; Version: number }>
): Promise<number> {
  let optionsCount = 0

  for (let optionIndex = 0; optionIndex < options.length; optionIndex++) {
    const option = options[optionIndex]
    await createSessionOption(new SessionOptionModel({
      SessionId: sessionId,
      QuestionId: questionId,
      QuestionVersion: questionVersion,
      OptionId: option.Id,
      OptionVersion: option.Version,
      PresentationIndex: optionIndex
    }))
    optionsCount++
  }

  return optionsCount
}

/**
 * Saves a single question and its options to the database
 * @param sessionId The session ID
 * @param question The question to save
 * @param presentationIndex The presentation index for this question
 * @returns Number of options saved for this question
 */
async function saveSessionQuestion(
  sessionId: string,
  question: { Id: string; Version: number; Options?: Array<{ Id: string; Version: number }>; SectionDisplayIndex?: number; DisplayIndexWithinSection?: number; QuestionSetId?: string; QuestionSetVersion?: number },
  presentationIndex: number
): Promise<number> {
  // Create session question record
  const questionData: EvalSessionQuestion = {
    SessionId: sessionId,
    QuestionId: question.Id,
    QuestionVersion: question.Version,
    PresentationIndex: presentationIndex
  }

  // For dynamic questions, save the QuestionSetId and QuestionSetVersion
  if (question.QuestionSetId && question.QuestionSetVersion) {
    questionData.QuestionSetId = question.QuestionSetId
    questionData.QuestionSetVersion = question.QuestionSetVersion
  }

  await createSessionQuestion(new SessionQuestionModel(questionData))

  // Save options if they exist
  if (question.Options && question.Options.length > 0) {
    return await saveQuestionOptions(sessionId, question.Id, question.Version, question.Options)
  }

  return 0
}

/**
 * Saves sub-questions and their options to the database
 * @param sessionId The session ID
 * @param subQuestions Array of sub-questions to save
 * @param startingPresentationIndex The starting presentation index
 * @returns Object containing the next presentation index and total options count
 */
async function saveSubQuestions(
  sessionId: string,
  subQuestions: Array<{ Id: string; Version: number; Options?: Array<{ Id: string; Version: number }> }>,
  startingPresentationIndex: number
): Promise<{ nextPresentationIndex: number; optionsCount: number }> {
  let presentationIndex = startingPresentationIndex
  let totalOptionsCount = 0

  for (const subQuestion of subQuestions) {
    const optionsCount = await saveSessionQuestion(sessionId, subQuestion, presentationIndex++)
    totalOptionsCount += optionsCount
  }

  return { nextPresentationIndex: presentationIndex, optionsCount: totalOptionsCount }
}

/**
 * Processes and saves all questions (main questions and sub-questions) for an assessment
 * @param sessionId The session ID
 * @param questions Array of questions to save
 * @returns Total number of options saved
 */
async function processAssessmentQuestions(
  sessionId: string,
  questions: Array<{
    Id: string;
    Version: number;
    Options?: Array<{ Id: string; Version: number }>;
    SubQuestions?: Array<{ Id: string; Version: number; Options?: Array<{ Id: string; Version: number }> }>;
  }>
): Promise<number> {
  let totalOptionsCount = 0
  let globalPresentationIndex = 0

  for (const question of questions) {
    // Save main question
    const mainQuestionOptionsCount = await saveSessionQuestion(sessionId, question, globalPresentationIndex++)
    totalOptionsCount += mainQuestionOptionsCount

    // Save sub-questions if they exist
    if (question.SubQuestions && question.SubQuestions.length > 0) {
      const subQuestionResult = await saveSubQuestions(sessionId, question.SubQuestions, globalPresentationIndex)
      globalPresentationIndex = subQuestionResult.nextPresentationIndex
      totalOptionsCount += subQuestionResult.optionsCount
    }
  }

  return totalOptionsCount
}

/**
 * Handles validation errors and creates user-friendly error messages
 * @param error The validation error
 * @param request The original request
 */
function handleValidationError(error: z.ZodError, request: SaveAssessmentRequest): never {
  const errorMessage = prettifyError(error)
  log('warn', 'Invalid request data for save assessment', {
    errorMessage,
    evalId: request.evalId,
    userId: request.userId
  })
  throw new Error(`Invalid request data: ${errorMessage}`)
}

/**
 * Saves an assessment as a session with questions and options.
 * This service creates records in the following order:
 * 1. EVAL_Sessions - creates a new session instance for the student
 * 2. EVAL_SessionQuestions - saves each question with its presentation index
 * 3. EVAL_SessionOptions - saves each option for each question with its presentation index
 *
 * @param request The assessment data to save
 * @returns Promise containing the session ID and counts of saved records
 */
export default async function saveAssessment(request: SaveAssessmentRequest): Promise<SaveAssessmentResponse> {
  try {
    // 1. Validate input
    const validatedRequest = saveAssessmentRequestSchema.parse(request)
    const { evalId, evalVersion, userId, questions, sessionId, notes } = validatedRequest

    log('info', 'Starting assessment save process', {
      evalId,
      evalVersion,
      userId,
      questionsCount: questions.length,
      hasNotes: !!notes,
      eventType: EventType.evaluation_create
    })

    // 2. Process all questions (delegated to helper)
    const totalOptionsCount = await processAssessmentQuestions(sessionId, questions)

    // 3. Return response
    const response: SaveAssessmentResponse = {
      sessionId: sessionId,
      questionsCount: questions.length,
      optionsCount: totalOptionsCount
    }

    log('info', 'Successfully saved assessment', {response, eventType: EventType.evaluation_create})
    return response

  } catch (error) {
    // 4. Handle errors (delegated to helper)
    if (error instanceof z.ZodError) {
      handleValidationError(error, request)
    }

    log('error', 'Failed to save assessment', {
      error: getErrorMessage(error),
      evalId: request.evalId,
      userId: request,
      eventType: EventType.evaluation_create

    })
    throw error
  }
}
