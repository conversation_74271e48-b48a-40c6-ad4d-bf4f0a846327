import mssql from '@lcs/mssql-utility'
import { SessionQuestionModel } from '../../../models/session-question.model.js'
import { EvalSessionQuestion, SessionQuestionsTableName } from '@tess-f/sql-tables/dist/evaluations/session-questions.js'

/**
 * Get all session questions for a specific session, ordered by presentation index
 */
export default async function getSessionQuestions (sessionId: string): Promise<SessionQuestionModel[]> {
  const request = mssql.getPool().request()
  request.input('sessionId', sessionId)

  const results = await request.query<EvalSessionQuestion>(`
    SELECT * FROM [${SessionQuestionsTableName}]
    WHERE SessionId = @sessionId
    ORDER BY PresentationIndex ASC
  `)

  return results.recordset.map(record => new SessionQuestionModel(undefined, record))
}
