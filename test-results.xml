<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="33.650" tests="55" failures="0" skipped="4">
  <testsuite name="Root Suite" timestamp="2025-11-18T20:00:52" tests="0" time="0.000" failures="0">
  </testsuite>
  <testsuite name="HTTP get session data controller" timestamp="2025-11-18T20:00:52" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\checklist\get-session-data.controller.spec.ts" time="16.556" failures="0">
    <testcase name="HTTP get session data controller returns success if the request data is valid" time="16.037" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP get session data controller returns an error if the request data is invalid" time="0.183" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP get session data controller returns an internal server error if the request is rejected" time="0.235" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="HTTP init session controller" timestamp="2025-11-18T20:01:08" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\checklist\init-session.controller.spec.ts" time="0.459" failures="0">
    <testcase name="HTTP init session controller returns success if the request data is valid" time="0.129" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP init session controller returns an error if the request params are invalid" time="0.077" classname="returns an error if the request params are invalid">
    </testcase>
    <testcase name="HTTP init session controller returns an error if the request body is invalid" time="0.077" classname="returns an error if the request body is invalid">
    </testcase>
    <testcase name="HTTP init session controller returns an internal server error if the request is rejected" time="0.145" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="HTTP submit session controller" timestamp="2025-11-18T20:01:09" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\checklist\submit-session.controller.spec.ts" time="1.254" failures="0">
    <testcase name="HTTP submit session controller returns success if the request data is valid" time="0.708" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP submit session controller returns an error if the request data is invalid" time="0.300" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP submit session controller returns an internal server error if the request is rejected" time="0.237" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="HTTP get controller" timestamp="2025-11-18T20:01:10" tests="0" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\get.controller.spec.ts" time="0.000" failures="0">
  </testsuite>
  <testsuite name="Basic validation tests" timestamp="2025-11-18T20:01:10" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\get.controller.spec.ts" time="0.443" failures="0">
    <testcase name="HTTP get controller Basic validation tests returns success if the request data is valid" time="0.240" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP get controller Basic validation tests returns an error if the request data is invalid" time="0.091" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP get controller Basic validation tests returns an internal server error if the request is rejected" time="0.108" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="Assessment generation (EvaluationTypeId = 1)" timestamp="2025-11-18T20:01:10" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\get.controller.spec.ts" time="0.805" failures="0">
    <testcase name="HTTP get controller Assessment generation (EvaluationTypeId = 1) generates new assessment with sections when no existing session data" time="0.162" classname="generates new assessment with sections when no existing session data">
    </testcase>
    <testcase name="HTTP get controller Assessment generation (EvaluationTypeId = 1) loads existing assessment from session" time="0.334" classname="loads existing assessment from session">
    </testcase>
    <testcase name="HTTP get controller Assessment generation (EvaluationTypeId = 1) returns bad request when session is missing for assessment" time="0.083" classname="returns bad request when session is missing for assessment">
    </testcase>
    <testcase name="HTTP get controller Assessment generation (EvaluationTypeId = 1) sanitizes sub-question options in assessment" time="0.213" classname="sanitizes sub-question options in assessment">
    </testcase>
  </testsuite>
  <testsuite name="Checklist generation (EvaluationTypeId != 1)" timestamp="2025-11-18T20:01:11" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\get.controller.spec.ts" time="1.277" failures="0">
    <testcase name="HTTP get controller Checklist generation (EvaluationTypeId != 1) generates checklist with all questions" time="0.230" classname="generates checklist with all questions">
    </testcase>
    <testcase name="HTTP get controller Checklist generation (EvaluationTypeId != 1) sorts checklist questions by OrderId when RandomizeQuestions is false" time="0.372" classname="sorts checklist questions by OrderId when RandomizeQuestions is false">
    </testcase>
    <testcase name="HTTP get controller Checklist generation (EvaluationTypeId != 1) does not sort checklist questions when RandomizeQuestions is true" time="0.329" classname="does not sort checklist questions when RandomizeQuestions is true">
    </testcase>
    <testcase name="HTTP get controller Checklist generation (EvaluationTypeId != 1) picks subset of questions when IncludeAllQuestions is false" time="0.255" classname="picks subset of questions when IncludeAllQuestions is false">
    </testcase>
  </testsuite>
  <testsuite name="HTTP init assessment session controller" timestamp="2025-11-18T20:01:13" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\init-assessment-session.controller.spec.ts" time="1.023" failures="0">
    <testcase name="HTTP init assessment session controller creates new session and submits xAPI launch statement" time="0.254" classname="creates new session and submits xAPI launch statement">
    </testcase>
    <testcase name="HTTP init assessment session controller returns bad request if evaluation ID is invalid" time="0.239" classname="returns bad request if evaluation ID is invalid">
    </testcase>
    <testcase name="HTTP init assessment session controller returns internal server error if xAPI statement submission fails" time="0.230" classname="returns internal server error if xAPI statement submission fails">
    </testcase>
    <testcase name="HTTP init assessment session controller returns internal server error if database operation fails" time="0.264" classname="returns internal server error if database operation fails">
    </testcase>
  </testsuite>
  <testsuite name="HTTP search controller" timestamp="2025-11-18T20:01:14" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\search.controller.spec.ts" time="0.963" failures="0">
    <testcase name="HTTP search controller returns success if the request data is valid" time="0.677" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP search controller returns an error if the request data is invalid" time="0.092" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP search controller returns an internal server error if the request is rejected" time="0.153" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="Assessment Generator Service" timestamp="2025-11-18T20:01:15" tests="1" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\_LOCAL_assessment-generator-service.spec.ts" time="0.002" failures="0">
  </testsuite>
  <testsuite name="Assessment Generator Service" timestamp="2025-11-18T20:01:15" tests="6" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\assessment-generator-service.spec.ts" time="0.000" failures="0">
    <testcase name="Assessment Generator Service processes fixed questions without QuestionSetId" time="1.120" classname="processes fixed questions without QuestionSetId">
    </testcase>
    <testcase name="Assessment Generator Service processes dynamic questions with QuestionSetId" time="0.926" classname="processes dynamic questions with QuestionSetId">
    </testcase>
    <testcase name="Assessment Generator Service sorts questions by section DisplayIndex then question DisplayIndex" time="0.640" classname="sorts questions by section DisplayIndex then question DisplayIndex">
    </testcase>
    <testcase name="Assessment Generator Service randomizes questions within sections when RandomizeQuestions is true" time="0.781" classname="randomizes questions within sections when RandomizeQuestions is true">
    </testcase>
    <testcase name="Assessment Generator Service handles empty evaluation questions gracefully" time="0.777" classname="handles empty evaluation questions gracefully">
    </testcase>
    <testcase name="Assessment Generator Service handles mixed fixed and dynamic questions" time="0.928" classname="handles mixed fixed and dynamic questions">
    </testcase>
  </testsuite>
  <testsuite name="Scramble Options Feature" timestamp="2025-11-18T20:01:20" tests="7" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\assessment-generator-service.spec.ts" time="3.298" failures="0">
    <testcase name="Assessment Generator Service Scramble Options Feature scrambles options when ScrambleOptions is true" time="0.733" classname="scrambles options when ScrambleOptions is true">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature keeps options in OrderId order when ScrambleOptions is false" time="0.689" classname="keeps options in OrderId order when ScrambleOptions is false">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature respects locked options when scrambling" time="0.546" classname="respects locked options when scrambling">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature scrambles sub-question options when ScrambleOptions is true" time="0.384" classname="scrambles sub-question options when ScrambleOptions is true">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature handles questions with no options" time="0.373" classname="handles questions with no options">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature handles questions with all locked options" time="0.314" classname="handles questions with all locked options">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature handles match questions with source/target separation (fallback logic)" time="0.241" classname="handles match questions with source/target separation (fallback logic)">
    </testcase>
  </testsuite>
  <testsuite name="Assessment Loader Service" timestamp="2025-11-18T20:01:23" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\assessment-loader-service.spec.ts" time="0.262" failures="0">
    <testcase name="Assessment Loader Service should load assessment with questions and options in correct order" time="0.084" classname="should load assessment with questions and options in correct order">
    </testcase>
    <testcase name="Assessment Loader Service should return empty array when no session questions found" time="0.049" classname="should return empty array when no session questions found">
    </testcase>
    <testcase name="Assessment Loader Service should handle questions with sub-questions" time="0.070" classname="should handle questions with sub-questions">
    </testcase>
    <testcase name="Assessment Loader Service should handle errors gracefully" time="0.053" classname="should handle errors gracefully">
    </testcase>
  </testsuite>
  <testsuite name="Save Assessment Service - Integration Tests" timestamp="2025-11-18T20:01:23" tests="2" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\save-assessment-service.integration.spec.ts" time="0.986" failures="0">
  </testsuite>
  <testsuite name="Save Assessment Service" timestamp="2025-11-18T20:01:24" tests="5" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\save-assessment-service.spec.ts" time="0.834" failures="0">
    <testcase name="Save Assessment Service should successfully save an assessment with questions and options" time="0.411" classname="should successfully save an assessment with questions and options">
    </testcase>
    <testcase name="Save Assessment Service should handle questions without options" time="0.181" classname="should handle questions without options">
    </testcase>
    <testcase name="Save Assessment Service should throw error for invalid request data" time="0.092" classname="should throw error for invalid request data">
    </testcase>
    <testcase name="Save Assessment Service should save dynamic questions with QuestionSetId and QuestionSetVersion" time="0.121" classname="should save dynamic questions with QuestionSetId and QuestionSetVersion">
    </testcase>
  </testsuite>
  <testsuite name="Session Get Service" timestamp="2025-11-18T20:01:25" tests="0" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\mssql\session\get.service.spec.ts" time="0.000" failures="0">
  </testsuite>
  <testsuite name="sessionHasAssessmentData" timestamp="2025-11-18T20:01:25" tests="2" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\mssql\session\get.service.spec.ts" time="0.096" failures="0">
    <testcase name="Session Get Service sessionHasAssessmentData should return true when session has questions" time="0.058" classname="should return true when session has questions">
    </testcase>
    <testcase name="Session Get Service sessionHasAssessmentData should return false when session has no questions" time="0.036" classname="should return false when session has no questions">
    </testcase>
  </testsuite>
</testsuites>