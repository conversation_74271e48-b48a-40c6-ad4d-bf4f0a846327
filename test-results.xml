<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="23.713" tests="55" failures="0" skipped="4">
  <testsuite name="Root Suite" timestamp="2025-11-18T20:14:25" tests="0" time="0.000" failures="0">
  </testsuite>
  <testsuite name="HTTP get session data controller" timestamp="2025-11-18T20:14:25" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\checklist\get-session-data.controller.spec.ts" time="14.153" failures="0">
    <testcase name="HTTP get session data controller returns success if the request data is valid" time="13.840" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP get session data controller returns an error if the request data is invalid" time="0.142" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP get session data controller returns an internal server error if the request is rejected" time="0.104" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="HTTP init session controller" timestamp="2025-11-18T20:14:39" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\checklist\init-session.controller.spec.ts" time="0.292" failures="0">
    <testcase name="HTTP init session controller returns success if the request data is valid" time="0.091" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP init session controller returns an error if the request params are invalid" time="0.052" classname="returns an error if the request params are invalid">
    </testcase>
    <testcase name="HTTP init session controller returns an error if the request body is invalid" time="0.053" classname="returns an error if the request body is invalid">
    </testcase>
    <testcase name="HTTP init session controller returns an internal server error if the request is rejected" time="0.085" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="HTTP submit session controller" timestamp="2025-11-18T20:14:39" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\checklist\submit-session.controller.spec.ts" time="1.345" failures="0">
    <testcase name="HTTP submit session controller returns success if the request data is valid" time="0.622" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP submit session controller returns an error if the request data is invalid" time="0.339" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP submit session controller returns an internal server error if the request is rejected" time="0.373" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="HTTP get controller" timestamp="2025-11-18T20:14:41" tests="0" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\get.controller.spec.ts" time="0.000" failures="0">
  </testsuite>
  <testsuite name="Basic validation tests" timestamp="2025-11-18T20:14:41" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\get.controller.spec.ts" time="0.374" failures="0">
    <testcase name="HTTP get controller Basic validation tests returns success if the request data is valid" time="0.239" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP get controller Basic validation tests returns an error if the request data is invalid" time="0.070" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP get controller Basic validation tests returns an internal server error if the request is rejected" time="0.064" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="Assessment generation (EvaluationTypeId = 1)" timestamp="2025-11-18T20:14:41" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\get.controller.spec.ts" time="0.392" failures="0">
    <testcase name="HTTP get controller Assessment generation (EvaluationTypeId = 1) generates new assessment with sections when no existing session data" time="0.095" classname="generates new assessment with sections when no existing session data">
    </testcase>
    <testcase name="HTTP get controller Assessment generation (EvaluationTypeId = 1) loads existing assessment from session" time="0.090" classname="loads existing assessment from session">
    </testcase>
    <testcase name="HTTP get controller Assessment generation (EvaluationTypeId = 1) returns bad request when session is missing for assessment" time="0.067" classname="returns bad request when session is missing for assessment">
    </testcase>
    <testcase name="HTTP get controller Assessment generation (EvaluationTypeId = 1) sanitizes sub-question options in assessment" time="0.137" classname="sanitizes sub-question options in assessment">
    </testcase>
  </testsuite>
  <testsuite name="Checklist generation (EvaluationTypeId != 1)" timestamp="2025-11-18T20:14:41" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\get.controller.spec.ts" time="0.507" failures="0">
    <testcase name="HTTP get controller Checklist generation (EvaluationTypeId != 1) generates checklist with all questions" time="0.145" classname="generates checklist with all questions">
    </testcase>
    <testcase name="HTTP get controller Checklist generation (EvaluationTypeId != 1) sorts checklist questions by OrderId when RandomizeQuestions is false" time="0.126" classname="sorts checklist questions by OrderId when RandomizeQuestions is false">
    </testcase>
    <testcase name="HTTP get controller Checklist generation (EvaluationTypeId != 1) does not sort checklist questions when RandomizeQuestions is true" time="0.125" classname="does not sort checklist questions when RandomizeQuestions is true">
    </testcase>
    <testcase name="HTTP get controller Checklist generation (EvaluationTypeId != 1) picks subset of questions when IncludeAllQuestions is false" time="0.104" classname="picks subset of questions when IncludeAllQuestions is false">
    </testcase>
  </testsuite>
  <testsuite name="HTTP init assessment session controller" timestamp="2025-11-18T20:14:42" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\init-assessment-session.controller.spec.ts" time="0.318" failures="0">
    <testcase name="HTTP init assessment session controller creates new session and submits xAPI launch statement" time="0.104" classname="creates new session and submits xAPI launch statement">
    </testcase>
    <testcase name="HTTP init assessment session controller returns bad request if evaluation ID is invalid" time="0.068" classname="returns bad request if evaluation ID is invalid">
    </testcase>
    <testcase name="HTTP init assessment session controller returns internal server error if xAPI statement submission fails" time="0.077" classname="returns internal server error if xAPI statement submission fails">
    </testcase>
    <testcase name="HTTP init assessment session controller returns internal server error if database operation fails" time="0.064" classname="returns internal server error if database operation fails">
    </testcase>
  </testsuite>
  <testsuite name="HTTP search controller" timestamp="2025-11-18T20:14:42" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\controllers\http\evaluations\search.controller.spec.ts" time="0.326" failures="0">
    <testcase name="HTTP search controller returns success if the request data is valid" time="0.206" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP search controller returns an error if the request data is invalid" time="0.058" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP search controller returns an internal server error if the request is rejected" time="0.059" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
  <testsuite name="Assessment Generator Service" timestamp="2025-11-18T20:14:42" tests="1" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\_LOCAL_assessment-generator-service.spec.ts" time="0.001" failures="0">
  </testsuite>
  <testsuite name="Assessment Generator Service" timestamp="2025-11-18T20:14:42" tests="6" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\assessment-generator-service.spec.ts" time="0.000" failures="0">
    <testcase name="Assessment Generator Service processes fixed questions without QuestionSetId" time="0.359" classname="processes fixed questions without QuestionSetId">
    </testcase>
    <testcase name="Assessment Generator Service processes dynamic questions with QuestionSetId" time="0.366" classname="processes dynamic questions with QuestionSetId">
    </testcase>
    <testcase name="Assessment Generator Service sorts questions by section DisplayIndex then question DisplayIndex" time="0.372" classname="sorts questions by section DisplayIndex then question DisplayIndex">
    </testcase>
    <testcase name="Assessment Generator Service randomizes questions within sections when RandomizeQuestions is true" time="0.191" classname="randomizes questions within sections when RandomizeQuestions is true">
    </testcase>
    <testcase name="Assessment Generator Service handles empty evaluation questions gracefully" time="0.203" classname="handles empty evaluation questions gracefully">
    </testcase>
    <testcase name="Assessment Generator Service handles mixed fixed and dynamic questions" time="0.284" classname="handles mixed fixed and dynamic questions">
    </testcase>
  </testsuite>
  <testsuite name="Scramble Options Feature" timestamp="2025-11-18T20:14:44" tests="7" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\assessment-generator-service.spec.ts" time="2.206" failures="0">
    <testcase name="Assessment Generator Service Scramble Options Feature scrambles options when ScrambleOptions is true" time="0.406" classname="scrambles options when ScrambleOptions is true">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature keeps options in OrderId order when ScrambleOptions is false" time="0.249" classname="keeps options in OrderId order when ScrambleOptions is false">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature respects locked options when scrambling" time="0.267" classname="respects locked options when scrambling">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature scrambles sub-question options when ScrambleOptions is true" time="0.427" classname="scrambles sub-question options when ScrambleOptions is true">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature handles questions with no options" time="0.281" classname="handles questions with no options">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature handles questions with all locked options" time="0.261" classname="handles questions with all locked options">
    </testcase>
    <testcase name="Assessment Generator Service Scramble Options Feature handles match questions with source/target separation (fallback logic)" time="0.309" classname="handles match questions with source/target separation (fallback logic)">
    </testcase>
  </testsuite>
  <testsuite name="Assessment Loader Service" timestamp="2025-11-18T20:14:46" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\assessment-loader-service.spec.ts" time="0.219" failures="0">
    <testcase name="Assessment Loader Service should load assessment with questions and options in correct order" time="0.072" classname="should load assessment with questions and options in correct order">
    </testcase>
    <testcase name="Assessment Loader Service should return empty array when no session questions found" time="0.039" classname="should return empty array when no session questions found">
    </testcase>
    <testcase name="Assessment Loader Service should handle questions with sub-questions" time="0.060" classname="should handle questions with sub-questions">
    </testcase>
    <testcase name="Assessment Loader Service should handle errors gracefully" time="0.043" classname="should handle errors gracefully">
    </testcase>
  </testsuite>
  <testsuite name="Save Assessment Service - Integration Tests" timestamp="2025-11-18T20:14:47" tests="2" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\save-assessment-service.integration.spec.ts" time="1.124" failures="0">
  </testsuite>
  <testsuite name="Save Assessment Service" timestamp="2025-11-18T20:14:48" tests="5" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\internal\save-assessment-service.spec.ts" time="0.513" failures="0">
    <testcase name="Save Assessment Service should successfully save an assessment with questions and options" time="0.209" classname="should successfully save an assessment with questions and options">
    </testcase>
    <testcase name="Save Assessment Service should handle questions without options" time="0.104" classname="should handle questions without options">
    </testcase>
    <testcase name="Save Assessment Service should throw error for invalid request data" time="0.079" classname="should throw error for invalid request data">
    </testcase>
    <testcase name="Save Assessment Service should save dynamic questions with QuestionSetId and QuestionSetVersion" time="0.110" classname="should save dynamic questions with QuestionSetId and QuestionSetVersion">
    </testcase>
  </testsuite>
  <testsuite name="Session Get Service" timestamp="2025-11-18T20:14:48" tests="0" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\mssql\session\get.service.spec.ts" time="0.000" failures="0">
  </testsuite>
  <testsuite name="sessionHasAssessmentData" timestamp="2025-11-18T20:14:48" tests="2" file="C:\Users\<USER>\.local\Dev\TMS\alt\Eval-Engine\src\services\mssql\session\get.service.spec.ts" time="0.116" failures="0">
    <testcase name="Session Get Service sessionHasAssessmentData should return true when session has questions" time="0.071" classname="should return true when session has questions">
    </testcase>
    <testcase name="Session Get Service sessionHasAssessmentData should return false when session has no questions" time="0.041" classname="should return false when session has no questions">
    </testcase>
  </testsuite>
</testsuites>